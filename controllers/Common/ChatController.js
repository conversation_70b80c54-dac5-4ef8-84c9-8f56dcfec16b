// const firebaseChatService = require("../../services/firebaseChatService");
const socketService = require("../../services/socketService");
const { db, admin } = require("../../config/firebase");
const User = require("../../models/User/User");
const Chat = require("../../models/Common/Chat");
const Invitation = require("../../models/User/invitations");
const { default: mongoose } = require("mongoose");
const haversine = require("haversine");

/**
 * Chat Controller
 * Handles all chat-related API endpoints
 */
class ChatController {
  // Socket.IO service for real-time messaging and direct MongoDB access
  constructor() {
    // No need to initialize socketService here as we're importing it at the top
  }

  /**
   * Initialize chat for a meeting
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async initializeChat(req, res) {
    try {
      const { invitationId } = req.params;

      // Validate meeting exists
      const meeting = await Invitation.findById(invitationId).populate([
        { path: "users.userId", select: "-password" },
        { path: "invitationBy", select: "-password" },
        { path: "feedbackId" },
        { path: "businessId" },
        { path: "rescheduleBy", select: "-password" },
      ]);

      if (!meeting) {
        return res
          .status(404)
          .json({ success: false, message: "Meeting not found" });
      }
      // // console.log("meeting", meeting);
      // Check if user is a participant or creator
      const userId = req.user._id;
      const isCreator =
        meeting.invitationBy._id.toString() === userId.toString();
      const isParticipant = meeting.users.some(
        (p) =>
          p.userId._id.toString() === userId.toString() &&
          p.status === "Accepted"
      );
      // console.log("isCreator", isCreator);
      // console.log("isParticipant", isParticipant);
      if (!isCreator && !isParticipant) {
        return res.status(403).json({
          success: false,
          message: "You are not authorized to access this chat",
        });
      }

      // Get participants for this meeting
      const participants = [meeting.invitationBy._id.toString()];
      meeting.users.forEach((p) => {
        if (p.status === "Accepted") {
          participants.push(p.userId._id.toString());
        }
      });

      // For 1-on-1 chats, check if a chat already exists between these users
      let existingChat = null;
      const isGroup = meeting.isGroup || participants.length > 2;

      // If this is a 1-on-1 chat (not a group), check if these users already have a chat
      if (!isGroup && participants.length === 2) {
        // This is a 1-on-1 chat, check if these users already have a chat
        existingChat = await Chat.findOne({
          participants: { $all: participants, $size: 2 },
          isGroupChat: false,
        });
      }

      // If no existing 1-on-1 chat or this is a group chat, check for a chat specific to this invitation
      if (!existingChat) {
        existingChat = await Chat.findOne({ invitationId });
      }
      // console.log("existingChat", existingChat);
      // If a chat exists, update it with the new invitation ID and return it
      if (existingChat) {
        const lastMessage =
          existingChat.messages.length > 0
            ? existingChat.messages[existingChat.messages.length - 1]
            : null;
        existingChat.lastMessage = lastMessage;
        // Update the chat with the latest invitation ID if it's different
        if (existingChat.invitationId.toString() !== invitationId) {
          existingChat.invitationId = invitationId;
          await existingChat.save();
        }

        // Return existing chat data
        const chatStatus = await socketService._getChatStatus(
          existingChat,
          userId.toString()
        );

        // Get participant details (excluding current user for 1-1 chats)
        const allParticipantIds = [];
        // const invitation = existingChat.invitationId;
        // Validate meeting exists
        const invitation = await Invitation.findById(invitationId).populate([
          { path: "users.userId", select: "-password" },
          { path: "invitationBy", select: "-password" },
          { path: "feedbackId" },
          { path: "businessId" },
          { path: "rescheduleBy", select: "-password" },
        ]);
        // console.log("invitation", invitation);
        // Add invitation creator if exists
        if (invitation?.invitationBy?._id) {
          allParticipantIds.push(invitation.invitationBy._id.toString());
        }

        // Add all users who accepted or pending the invitation
        if (invitation?.users && Array.isArray(invitation.users)) {
          invitation.users.forEach((user) => {
            if (
              (user?.status === "Accepted" || user?.status === "Pending") &&
              user?.userId?._id
            ) {
              allParticipantIds.push(user.userId._id.toString());
            }
          });
        }
        // console.log("allParticipantIds", allParticipantIds);
        // For one-to-one chat, filter out current user
        let participantIdsToQuery = [...allParticipantIds]; // Create a copy
        if (!existingChat?.isGroupChat) {
          participantIdsToQuery = allParticipantIds.filter(
            (id) => id?.toString() !== userId?.toString()
          );
        }
        // console.log("participantIdsToQuery", participantIdsToQuery);
        // Get participant details
        const otherParticipants = await User.find({
          _id: { $in: participantIdsToQuery },
        }).select("-password");
        // console.log("otherParticipants", otherParticipants);
        // Get unread message count
        const unreadCount = existingChat.messages.filter(
          (msg) =>
            msg.sender.toString() !== userId.toString() &&
            !msg.read.some(
              (r) => r.user.toString() === userId.toString() && r.readAt
            )
        ).length;

        // Get last message
        // const lastMessage =
        //   existingChat.messages.length > 0
        //     ? existingChat.messages[existingChat.messages.length - 1]
        //     : null;

        // Initialize tracking for each participant
        let participantStats = {};

        // Count unread messages for each participant
        existingChat.participants.forEach((p) => {
          const participantId = p.toString();
          // Count messages sent by others that this participant hasn't read
          const participantUnreadCount = existingChat.messages.filter(
            (msg) =>
              msg.sender.toString() !== participantId &&
              !msg.read.some(
                (r) => r.user.toString() === participantId && r.readAt
              )
          ).length;

          // Determine if participant has read the latest message
          const hasRead =
            existingChat.messages.length === 0 ||
            (existingChat.messages.length > 0 && participantUnreadCount === 0);

          participantStats[participantId] = {
            unreadCount: participantUnreadCount,
            hasRead: hasRead,
            lastReadTimestamp: null,
          };
        });

        // Check if invitation has been rescheduled
        const hasBeenRescheduled =
          existingChat.invitationId.rescheduleBy != null;

        // Create a simplified read status object for the response
        const readStatus = {};
        existingChat.participants.forEach((p) => {
          const participantId = p.toString();
          readStatus[participantId] =
            participantStats[participantId]?.hasRead || false;
        });

        // Use the calculated unreadCount for the current user
        const currentUserUnreadCount =
          participantStats[userId.toString()]?.unreadCount || 0;

        // Format the response to match getUserChats format
        const formattedChat = {
          _id: existingChat._id,
          invitationId: existingChat.invitationId._id,
          groupName: existingChat.invitationId.groupName,
          meetingTitle: existingChat.invitationId.title,
          scheduledTime: existingChat.invitationId.date,
          location: existingChat.invitationId.location || "",
          isGroupChat: existingChat.isGroupChat,
          participants: otherParticipants,
          status: chatStatus.status,
          statusColor: chatStatus.statusColor,
          statusMessage: chatStatus.statusMessage,
          unreadCount: currentUserUnreadCount,
          hasRescheduleInfo: hasBeenRescheduled,
          read: readStatus,
          participantStats: participantStats,
          lastMessage: lastMessage
            ? {
                content: lastMessage.content,
                sender: lastMessage.sender,
                timestamp: lastMessage.timestamp,
                isAudio: lastMessage.isAudio || false,
                isInfo: lastMessage.isInfo || false,
                audioUrl: lastMessage.audioUrl || null,
              }
            : null,
          chatOpenTime: existingChat.chatOpenTime,
          chatCloseTime: existingChat.chatCloseTime,
          canSendMessage: chatStatus.canSendMessage || false,
        };

        return res.status(200).json({
          success: true,
          chat: formattedChat,
        });
      }

      // Create new chat
      const meetingData = {
        _id: invitationId,
        participants,
        scheduledTime: meeting.date,
        isGroupMeeting: isGroup,
      };

      // Create new chat in MongoDB
      const newChat = new Chat({
        invitationId: invitationId,
        participants: participants,
        isGroupChat: isGroup,
        messages: [],
        active: true,
      });

      // Calculate chat open and close times based on meeting date using proper time arithmetic
      const meetingTime = new Date(meeting.date);
      const chatCloseTime = new Date(
        meetingTime.getTime() + 1 * 60 * 60 * 1000
      ); // 1 hour after meeting

      // Default to 3 hours before for standard users
      const chatOpenTime = new Date(meetingTime.getTime() - 3 * 60 * 60 * 1000);

      newChat.chatOpenTime = chatOpenTime;
      newChat.chatCloseTime = chatCloseTime;

      const chat = await newChat.save();
      // console.log("chat", chat);

      // Get chat status for UI
      const chatStatus = await socketService._getChatStatus(
        chat,
        userId.toString()
      );

      // Get participant details (excluding current user for 1-1 chats)
      const allParticipantIds = [];
      const invitation = await Invitation.findById(invitationId).populate([
        { path: "users.userId", select: "-password" },
        { path: "invitationBy", select: "-password" },
        { path: "feedbackId" },
        { path: "businessId" },
        { path: "rescheduleBy", select: "-password" },
      ]);

      // Add invitation creator if exists
      if (invitation?.invitationBy?._id) {
        allParticipantIds.push(invitation.invitationBy._id.toString());
      }

      // Add all users who accepted or pending the invitation
      if (invitation?.users && Array.isArray(invitation.users)) {
        invitation.users.forEach((user) => {
          if (
            (user?.status === "Accepted" || user?.status === "Pending") &&
            user?.userId?._id
          ) {
            allParticipantIds.push(user.userId._id.toString());
          }
        });
      }

      // For one-to-one chat, filter out current user
      let participantIdsToQuery = [...allParticipantIds]; // Create a copy
      if (!chat?.isGroupChat) {
        participantIdsToQuery = allParticipantIds.filter(
          (id) => id?.toString() !== userId?.toString()
        );
      }

      // Get participant details
      const otherParticipants = await User.find({
        _id: { $in: participantIdsToQuery },
      }).select("-password");

      // Initialize tracking for each participant
      let participantStats = {};

      // For a new chat, all participants have read all messages (there are none)
      chat.participants.forEach((p) => {
        const participantId = p.toString();
        participantStats[participantId] = {
          unreadCount: 0,
          hasRead: true, // For a new chat, everyone has read everything
          lastReadTimestamp: new Date(),
        };
      });

      // Check if invitation has been rescheduled
      const hasBeenRescheduled = invitation.rescheduleBy != null;

      // Create a simplified read status object for the response
      const readStatus = {};
      chat.participants.forEach((p) => {
        const participantId = p.toString();
        readStatus[participantId] =
          participantStats[participantId]?.hasRead || false;
      });

      // Format the response to match getUserChats format
      const formattedChat = {
        _id: chat._id,
        invitationId: invitation._id,
        groupName: invitation.groupName,
        meetingTitle: invitation.title,
        scheduledTime: invitation.date,
        location: invitation.location || "",
        isGroupChat: chat.isGroupChat,
        participants: otherParticipants,
        status: chatStatus.status,
        statusColor: chatStatus.statusColor,
        statusMessage: chatStatus.statusMessage,
        unreadCount: 0, // New chat has no unread messages
        hasRescheduleInfo: hasBeenRescheduled,
        read: readStatus,
        participantStats: participantStats,
        lastMessage: null, // New chat has no messages yet
        chatOpenTime: chat.chatOpenTime,
        chatCloseTime: chat.chatCloseTime,
        canSendMessage: chatStatus.canSendMessage || false,
      };

      return res.status(201).json({
        success: true,
        chat: formattedChat,
      });
    } catch (error) {
      console.error("Error initializing chat:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Get chat status
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async getChatStatus(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;

      // Get chat from MongoDB
      const chat = await Chat.findById(chatId).populate("invitationId");
      if (!chat) {
        return res.status(400).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Use socketService to get chat status
      const status = await socketService._getChatStatus(
        chat,
        userId.toString()
      );

      return res.status(200).json({
        success: true,
        status,
      });
    } catch (error) {
      console.error("Error getting chat status:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Get user's chats
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */

  async getUserChats(req, res) {
    try {
      const userId = req.user._id;
      const { userTime } = req.query;
      console.log("userTime", userTime);
      const currentUser = await User.findById(userId);

      console.log("currentUser", currentUser);
      const currentUserCoords = [
        currentUser.location?.coordinates[0],
        currentUser.location?.coordinates[1],
      ];
      // const currentUserCoords = [currentUser.latitude, currentUser.longitude];
      console.log("currentUserCoords", currentUserCoords);
      // Get all chats where user is a participant and hasn't deleted the chat
      const chats = await Chat.find({
        participants: userId,
        deletedBy: { $ne: userId }, // Exclude chats that the user has deleted
      })
        .populate({
          path: "invitationId",
          populate: [
            { path: "users.userId", select: "-password" },
            { path: "invitationBy", select: "-password" },
            { path: "feedbackId" },
            { path: "businessId" },
            { path: "rescheduleBy", select: "-password" },
          ],
        })
        .sort({ updatedAt: -1 });

      // Get status for each chat
      const chatsWithStatus = await Promise.all(
        chats.map(async (chat) => {
          // socketService is already imported at the top
          const status = await socketService._getChatStatus(
            chat,
            userId,
            userTime
          );
          // console.log("status", status);
          // Get all participants from the invitation
          const invitation = chat?.invitationId;
          const allParticipantIds = [];
          if (!invitation) {
            return;
          }

          // Add invitation creator if exists
          if (invitation?.invitationBy?._id) {
            allParticipantIds.push(invitation.invitationBy._id.toString());
          }

          // Add all users who accepted or pending the invitation
          if (invitation?.users && Array.isArray(invitation.users)) {
            invitation.users.forEach((user) => {
              if (
                (user?.status === "Accepted" || user?.status === "Pending") &&
                user?.userId?._id
              ) {
                allParticipantIds.push(user.userId._id.toString());
              }
            });
          }

          // For one-to-one chat, filter out current user
          let participantIdsToQuery = [...allParticipantIds]; // Create a copy
          if (!chat?.isGroupChat) {
            participantIdsToQuery = allParticipantIds.filter(
              (id) => id?.toString() !== userId?.toString()
            );
          }

          // Get participant details
          const otherParticipants = await User.find({
            _id: { $in: participantIdsToQuery },
          }).select("-password");

          const otherParticipantsWithDistance = otherParticipants.map(
            (user) => {
              const userCoords = user.location?.coordinates || [0, 0];
              console.log("userCoords", userCoords);
              const getUserDistance = (currentUserCoords, userCoords) => {
                const currentUserLatLon = {
                  latitude: currentUserCoords[0], // Latitude
                  longitude: currentUserCoords[1], // Longitude
                };

                const userLatLon = {
                  latitude: userCoords[0] || 0, // Latitude
                  longitude: userCoords[1] || 0, // Longitude
                };

                const distance = haversine(currentUserLatLon, userLatLon, {
                  unit: "km",
                });

                return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
              };
              const distance = getUserDistance(currentUserCoords, userCoords);
              console.log("distance", distance);
              return {
                ...user.toObject(),
                distance,
              };
            }
          );
          console.log(
            "otherParticipantsWithDistance",
            otherParticipantsWithDistance
          );
          console.log("otherParticipants", otherParticipants);

          // Get unread message count from MongoDB
          const unreadCount = chat.messages.filter(
            (msg) =>
              msg.sender.toString() !== userId.toString() &&
              !msg.read.some(
                (r) => r.user.toString() === userId.toString() && r.readAt
              )
          ).length;

          // Get last message from MongoDB
          const lastMessage =
            chat.messages.length > 0
              ? chat.messages[chat.messages.length - 1]
              : null;

          // Check if there's a Firebase chat room for more details
          // let firebaseLastMessage = null;
          let hasRescheduleInfo = false;
          // Initialize tracking for each participant
          let participantStats = {};

          // Initialize stats for each participant
          chat.participants.forEach((p) => {
            const participantId = p.toString();

            // Count messages sent by others that this participant hasn't read
            const participantUnreadCount = chat.messages.filter(
              (msg) =>
                msg.sender.toString() !== participantId &&
                !msg.read.some(
                  (r) => r.user.toString() === participantId && r.readAt
                )
            ).length;

            // Determine if participant has read the latest message
            const hasRead =
              chat.messages.length === 0 ||
              (chat.messages.length > 0 && participantUnreadCount === 0);

            // Find the last read timestamp if any
            let lastReadTimestamp = null;
            if (chat.messages.length > 0) {
              const lastReadMessage = [...chat.messages]
                .reverse()
                .find((msg) =>
                  msg.read.some(
                    (r) => r.user.toString() === participantId && r.readAt
                  )
                );
              if (lastReadMessage) {
                const readRecord = lastReadMessage.read.find(
                  (r) => r.user.toString() === participantId && r.readAt
                );
                lastReadTimestamp = readRecord?.readAt || null;
              }
            }

            participantStats[participantId] = {
              unreadCount: participantUnreadCount,
              hasRead: hasRead,
              lastReadTimestamp: lastReadTimestamp,
            };
          });
          // console.log("chat.invitationId", chat.invitationId);
          // Check if invitation has been rescheduled
          const hasBeenRescheduled = chat.invitationId?.rescheduleBy != null;

          // Get unread count for current user
          const currentUserUnreadCount =
            participantStats[userId.toString()]?.unreadCount || 0;

          // Create a simplified read status object for the response
          const readStatus = {};
          chat.participants.forEach((p) => {
            const participantId = p.toString();
            readStatus[participantId] =
              participantStats[participantId]?.hasRead || false;
          });

          // Construct the response object
          return {
            _id: chat._id,
            invitationId: chat.invitationId._id,
            // invitation: chat.invitationId,
            groupName: chat.invitationId.groupName,
            meetingTitle: chat.invitationId.title,
            scheduledTime: chat.invitationId.date,
            location: chat.invitationId.location || "",
            isGroupChat: chat.isGroupChat,
            participants: otherParticipantsWithDistance,
            status: status.status,
            statusColor: status.color,
            statusMessage: status.message,
            unreadCount: currentUserUnreadCount,
            hasRescheduleInfo: hasRescheduleInfo || hasBeenRescheduled,
            read: readStatus,
            participantStats: participantStats, // Include detailed stats for all participants
            // Prefer Firebase data if available, fall back to MongoDB data
            lastMessage: lastMessage
              ? {
                  content: lastMessage.content,
                  sender: lastMessage.sender,
                  timestamp: lastMessage.timestamp,
                  isAudio: lastMessage.isAudio || false,
                  isInfo: lastMessage.isInfo || false,
                  audioUrl: lastMessage.audioUrl || null,
                }
              : null,
            chatOpenTime: chat.chatOpenTime,
            chatCloseTime: chat.chatCloseTime,
            canSendMessage: status.canSendMessage || false,
          };
        })
      );

      return res.status(200).json({
        success: true,
        chats: chatsWithStatus,
      });
    } catch (error) {
      console.error("Error getting user chats:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Get chat messages
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async getChatMessages(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;
      // console.log("userId", userId);

      // Check if chat exists and user is a participant
      const chat = await Chat.findOne({
        _id: chatId,
        participants: { $in: [userId] },
      });

      // console.log("chat", chat);
      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found or you are not a participant",
        });
      }

      // Process messages to include read status information
      const messagesWithReadStatus = chat.messages.map((message) => {
        // Create a copy of the message object
        const messageObj = message.toObject
          ? message.toObject()
          : JSON.parse(JSON.stringify(message));

        // Add read status information for each participant
        const readStatus = {};

        // For each participant in the chat
        chat.participants.forEach((participantId) => {
          const participantIdStr = participantId.toString();
          // Check if the message has been read by this participant
          const isRead = socketService.isMessageReadByUser(
            message,
            participantIdStr
          );
          readStatus[participantIdStr] = isRead;
        });

        // Add read status to the message object
        messageObj.readStatus = readStatus;

        return messageObj;
      });

      // Mark messages as read for the requesting user
      // This will happen asynchronously, we don't need to wait for it
      socketService.markMessagesAsRead(chatId, userId);

      return res.status(200).json({
        success: true,
        chatId,
        status: chat.active ? "active" : "inactive",
        messages: messagesWithReadStatus,
        chat: {
          ...chat.toObject(),
          messages: messagesWithReadStatus,
        },
      });
    } catch (error) {
      console.error("Error getting chat messages:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Get chat data for re-invite
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async getChatForReInvite(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;

      // Check if chat exists and user is a participant
      const chat = await Chat.findOne({
        invitationId: chatId,
        participants: userId,
      });

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found or you are not a participant",
        });
      }

      // Get chat data for re-invite from MongoDB
      const chatData = await Chat.findById(chatId)
        .populate("participants", "userName profilePicture")
        .populate("invitationId");

      if (!chatData) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Format the chat data for re-invite
      const reInviteData = {
        chatId: chatData._id,
        participants: chatData.participants.map((p) => ({
          _id: p._id,
          userName: p.userName,
          profilePicture: p.profilePicture,
        })),
        invitationId: chatData.invitationId?._id,
        meetingDate: chatData.invitationId?.date,
        isGroupChat: chatData.isGroupChat,
        messages: chatData.messages.slice(-10), // Get last 10 messages for context
      };

      if (reInviteData.error) {
        return res.status(400).json({
          success: false,
          message: reInviteData.error,
        });
      }

      return res.status(200).json({
        success: true,
        reInviteData,
      });
    } catch (error) {
      console.error("Error getting chat for re-invite:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Send a message to a chat
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async sendMessage(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;
      const { content, isAudio, audioUrl, isInfo } = req.body;

      // Find the chat
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Check if user is a participant
      if (
        !chat.participants.includes(userId) &&
        chat.invitationId.creator.toString() !== userId.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to send messages to this chat",
        });
      }

      // Validate based on message type
      if (isAudio) {
        if (!audioUrl) {
          return res.status(400).json({
            success: false,
            message: "Audio URL is required for audio messages",
          });
        }
      } else if (!content || content.trim() === "") {
        return res.status(400).json({
          success: false,
          message: "Message content is required for text messages",
        });
      }

      // Skip message limit check for info messages
      if (!isInfo) {
        // Check message limit for non-premium users
        const user = await User.findById(userId);
        const isPremium = user && user.isPremium;

        if (!isPremium) {
          // Count messages sent by this user
          let messageCount = 0;
          const MESSAGE_LIMIT_NON_PREMIUM = 2;

          try {
            // For group chats
            if (chat.isGroupChat) {
              const messagesQuery = await db
                .collection("messages")
                .where("chatId", "==", chat.invitationId._id.toString())
                .where("fromId", "==", userId.toString())
                .get();

              messageCount = messagesQuery.size;
            }
            // For individual chats
            else if (chat.participants.length === 2) {
              const sortedParticipants = [...chat.participants]
                .map((p) => p.toString())
                .sort();
              const chatRoomId = sortedParticipants.join("_");

              const chatRoomDoc = await db
                .collection("individual_chatrooms")
                .doc(chatRoomId)
                .get();

              if (chatRoomDoc.exists) {
                const chatData = chatRoomDoc.data();
                // Count messages where fromId matches the current user
                if (chatData.messages && Array.isArray(chatData.messages)) {
                  messageCount = chatData.messages.filter(
                    (msg) => msg.fromId === userId.toString() && !msg.isInfo
                  ).length;
                }
              }
            }
          } catch (firebaseError) {
            console.warn(
              "Error counting messages from Firebase:",
              firebaseError
            );
            // Fall back to MongoDB count if Firebase fails
            messageCount = chat.messages.filter(
              (msg) =>
                msg.sender.toString() === userId.toString() && !msg.isInfo
            ).length;
          }

          // Check if user can send more messages
          if (messageCount >= MESSAGE_LIMIT_NON_PREMIUM) {
            return res.status(403).json({
              success: false,
              message: "Message limit reached for non-premium users",
              isPremium: false,
              messageCount,
              messageLimit: MESSAGE_LIMIT_NON_PREMIUM,
              upgradeRequired: true,
            });
          }
        }
      }

      // Create message data
      const messageData = {
        chatId,
        senderId: userId.toString(),
        content: content ? content.trim() : "",
        timestamp: new Date(),
        isAudio,
        audioUrl: audioUrl || null,
        isInfo,
      };

      // Process the message
      // First get the chat
      const chatDoc = await Chat.findById(chatId).populate("invitationId");
      if (!chat) {
        return res.status(400).json({
          success: false,
          message: "Chat not found. Please initialize the chat first.",
        });
      }

      // Check if sender is a participant
      if (!chatDoc.participants.includes(userId.toString())) {
        return res.status(403).json({
          success: false,
          message: "User is not a participant in this chat",
        });
      }

      // Get sender's user data
      const sender = await User.findById(userId).select("isPremium userName");
      if (!sender) {
        return res.status(404).json({
          success: false,
          message: "Sender not found",
        });
      }

      const now = new Date();
      const isPremiumSender = sender.isPremium;

      // Check if chat is within time window
      if (chatDoc.chatOpenTime && now < chatDoc.chatOpenTime) {
        // Chat not open yet
        if (isPremiumSender) {
          // Premium users can send messages early
          if (!chatDoc.premiumEarlyStart) {
            chatDoc.premiumEarlyStart = true;
            await chatDoc.save();
          }
        } else {
          // Standard user trying to send message before chat is open
          if (!chatDoc.premiumEarlyStart) {
            // No premium user has started the chat early
            return res.status(400).json({
              success: false,
              message: "Chat not open yet",
              openTime: chatDoc.chatOpenTime,
              isPremium: isPremiumSender,
            });
          } else {
            // A premium user has started the chat early
            // Check if standard user has already sent 2 messages
            const userMessageCount = chatDoc.messages.filter(
              (msg) => msg.senderId.toString() === userId.toString()
            ).length;

            if (userMessageCount >= 2) {
              return res.status(400).json({
                success: false,
                message:
                  "Message limit reached. Upgrade to Premium for unlimited messages.",
                isPremium: isPremiumSender,
              });
            }
          }
        }
      } else if (chatDoc.chatCloseTime && now > chatDoc.chatCloseTime) {
        // Chat closed
        return res.status(400).json({
          success: false,
          message: "Chat is closed",
          closeTime: chatDoc.chatCloseTime,
        });
      }

      // If it's a group chat, check if it has at least two participants who accepted
      if (chatDoc.isGroupChat) {
        const meeting = chatDoc.invitationId;
        if (!meeting) {
          return res.status(404).json({
            success: false,
            message: "Meeting not found",
          });
        }

        const acceptedUsers = meeting.users.filter(
          (p) => p.status === "Accepted"
        );
        if (acceptedUsers.length < 2) {
          return res.status(400).json({
            success: false,
            message: "Group chat requires at least two accepted participants",
            acceptedCount: acceptedUsers.length,
          });
        }
      }

      // Create read status for all participants
      const readStatus = chatDoc.participants.map((participantId) => ({
        user: participantId,
        readAt: participantId === userId.toString() ? now : null,
      }));

      // Create the message object
      const newMessage = {
        messageId: new mongoose.Types.ObjectId(),
        senderId: userId.toString(),
        senderName: sender.userName,
        content: content ? content.trim() : "",
        timestamp: now,
        isAudio: isAudio || false,
        audioUrl: audioUrl || null,
        isInfo: isInfo || false,
        read: readStatus,
      };

      // Add message to chat
      chatDoc.messages.push(newMessage);
      await chatDoc.save();

      // Emit socket event for real-time updates
      socketService.emitNewMessage(chatDoc._id.toString(), newMessage);

      const result = {
        success: true,
        message: newMessage,
      };

      if (result.error) {
        return res.status(400).json({
          success: false,
          message: result.error,
          details: result,
        });
      }

      return res.status(201).json({
        success: true,
        message: "Message sent successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error sending message:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Upload audio message
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async uploadAudioMessage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "Audio file is required",
        });
      }

      const userId = req.user._id.toString();
      const file = req.file;

      // Upload to Firebase Storage
      const bucket = admin.storage().bucket();
      const fileName = `chat-audio/${userId}/${Date.now()}-${
        file.originalname
      }`;

      // Create a file in the bucket
      const fileUpload = bucket.file(fileName);

      // Create a stream to upload the file
      const stream = fileUpload.createWriteStream({
        metadata: {
          contentType: file.mimetype,
        },
      });

      // Handle errors during upload
      stream.on("error", (error) => {
        console.error("Error uploading audio:", error);
        return res.status(500).json({
          success: false,
          message: "Error uploading audio file",
          error: error.message,
        });
      });

      // When upload completes, get the public URL
      stream.on("finish", async () => {
        // Make the file publicly accessible
        await fileUpload.makePublic();

        // Get the public URL
        const audioUrl = `https://storage.googleapis.com/${bucket.name}/${fileName}`;

        return res.status(200).json({
          success: true,
          message: "Audio uploaded successfully",
          audioUrl,
        });
      });

      // Pipe the file buffer to the stream
      stream.end(file.buffer);
    } catch (error) {
      console.error("Error uploading audio:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Mark messages as read
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async markMessagesAsRead(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;

      // Find the chat
      const chat = await Chat.findById(chatId);

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Check if user is a participant
      if (!chat.participants.some((p) => p.toString() === userId.toString())) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to access this chat",
        });
      }

      // Use the new API function to mark messages as read
      const result = await socketService.markMessagesAsRead(chatId, userId);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.message || "Failed to mark messages as read",
          error: result.error,
        });
      }

      return res.status(200).json({
        success: true,
        count: result.count,
        message: "Messages marked as read successfully",
        updatedMessages: result.updatedMessages,
      });
    } catch (error) {
      console.error("Error marking messages as read:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Handle invitation acceptance and send info messages
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async handleInvitationAccepted(req, res) {
    try {
      const { invitationId } = req.params;
      const acceptingUserId = req.user._id;
      const acceptingUserName = req.user.userName || req.user.name || "User";

      // Find the invitation
      const invitation = await Invitation.findById(invitationId);

      if (!invitation) {
        return res.status(404).json({
          success: false,
          message: "Invitation not found",
        });
      }

      const creatorId = invitation.invitationBy;
      const isGroup = invitation.isGroup || false;

      // Find or create the chat for this invitation
      let chat = await Chat.findOne({ invitationId });

      if (!chat) {
        // Get participants for this invitation
        const participants = [creatorId.toString()];
        invitation.users.forEach((p) => {
          if (
            p.status === "Accepted" ||
            p.userId.toString() === acceptingUserId.toString()
          ) {
            participants.push(p.userId.toString());
          }
        });

        // Create a new chat
        chat = await Chat.create({
          invitationId,
          participants,
          isGroupChat: isGroup,
          active: true,
        });
      }
      // console.log("acceptingUserId", acceptingUserId);
      // console.log("creatorId", creatorId);
      // console.log("isGroup", isGroup);
      // Use the Socket.IO service to send status update messages
      const result = await socketService.sendInvitationResponseMessages(
        invitationId.toString(),
        "accepted",
        acceptingUserId.toString(),
        creatorId.toString(),
        isGroup
      );

      // Return success response
      return res.status(200).json({
        success: true,
        message: "Invitation accepted and chat initialized",
        chatId: chat._id,
      });
    } catch (error) {
      console.error("Error handling invitation acceptance:", error);
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Delete a chat
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async deleteChat(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;

      // Find the chat
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Check if user is a participant
      if (!chat.participants.some((p) => p.toString() === userId.toString())) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to delete this chat",
        });
      }

      // Add user to deletedBy array if not already there
      if (!chat.deletedBy.includes(userId)) {
        chat.deletedBy.push(userId);
        await chat.save();
      }

      return res.json({
        success: true,
        message: "Chat removed from your chat list successfully",
      });
    } catch (error) {
      console.error("Error deleting chat:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Check for pending invitations between users
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async checkPendingInvitations(req, res) {
    try {
      const { userId } = req.params; // Target user ID
      const currentUserId = req.user._id;

      // Find pending invitations where current user is the creator and target user is invited
      const sentInvitations = await Invitation.findOne({
        invitationBy: currentUserId,
        "users.userId": userId,
        "users.status": "Pending",
        status: "Pending",
      }).populate([
        { path: "users.userId", select: "-password" },
        { path: "invitationBy", select: "-password" },
        { path: "feedbackId" },
        { path: "businessId" },
        { path: "rescheduleBy", select: "-password" },
      ]);

      // Find pending invitations where target user is the creator and current user is invited
      const receivedInvitations = await Invitation.findOne({
        invitationBy: userId,
        "users.userId": currentUserId,
        "users.status": "Pending",
        status: "Pending",
      }).populate([
        { path: "users.userId", select: "-password" },
        { path: "invitationBy", select: "-password" },
        { path: "feedbackId" },
        { path: "businessId" },
        { path: "rescheduleBy", select: "-password" },
      ]);

      return res.json({
        success: true,
        pendingInvitations: {
          sent: sentInvitations || null,
          received: receivedInvitations || null,
        },
        hasPendingInvitations: !!sentInvitations || !!receivedInvitations,
      });
    } catch (error) {
      console.error("Error checking pending invitations:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Check if a chat exists between two users
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response with chat details if found
   */
  /**
   * Helper method to get chat status
   * @param {Object} chat - Chat object
   * @param {String} userId - User ID
   * @returns {Object} - Chat status
   * @private
   */
  static async _getTwoUserChatStatus(chat, userId) {
    try {
      // Get user data to check if premium
      const user = await User.findById(userId).select("isPremium");

      if (!chat.chatOpenTime || !chat.chatCloseTime) {
        // If chat doesn't have open/close times, it's always open
        return {
          status: "Open now",
          color: "39FF14", // Neon green
          message: "Chat is open for coordination.",
          canSendMessage: true,
        };
      }

      const now = new Date();
      const chatOpenTime = new Date(chat.chatOpenTime);
      const chatCloseTime = new Date(chat.chatCloseTime);

      let status, color, message, canSendMessage;

      if (now < chatOpenTime) {
        // Chat not open yet - calculate time until open
        const timeUntilOpen = chatOpenTime.getTime() - now.getTime();
        const minutesUntilOpen = Math.ceil(timeUntilOpen / (1000 * 60));
        const hoursUntilOpen = Math.ceil(timeUntilOpen / (1000 * 60 * 60));

        let timeText = "";
        if (minutesUntilOpen < 60) {
          timeText = `${minutesUntilOpen} minute${
            minutesUntilOpen > 1 ? "s" : ""
          }`;
        } else if (hoursUntilOpen < 24) {
          timeText = `${hoursUntilOpen} hour${hoursUntilOpen > 1 ? "s" : ""}`;
        } else {
          const daysUntilOpen = Math.floor(hoursUntilOpen / 24);
          timeText = `${daysUntilOpen} day${daysUntilOpen > 1 ? "s" : ""}`;
        }

        status = `Opens in ${timeText}`;
        color = "808080"; // Grey

        if (user?.isPremium) {
          message =
            "The chat will open automatically 24 hours before your meetup.";
        } else {
          message =
            "The chat will open automatically 3 hours before your meetup.";
        }

        canSendMessage = false;
      } else if (now > chatCloseTime) {
        // Chat closed
        status = "Closed";
        color = "FF0000"; // Red
        message = "This chat is now closed.";
        canSendMessage = false;
      } else {
        // Chat open
        status = "Open now";
        color = "39FF14"; // Neon green
        message = "Chat is open for coordination.";
        canSendMessage = true;
      }

      return {
        status,
        color,
        message,
        canSendMessage,
        isPremium: user?.isPremium || false,
        chatOpenTime: chatOpenTime,
        chatCloseTime: chatCloseTime,
      };
    } catch (error) {
      console.error("Error getting chat status:", error);
      return {
        status: "Unknown",
        color: "808080",
        message: "Unable to determine chat status",
        canSendMessage: false,
      };
    }
  }
  async checkExistingChat(req, res) {
    try {
      const { userId1, userId2 } = req.query;

      if (!userId1 || !userId2) {
        return res.status(400).json({
          success: false,
          message: "Both user IDs are required",
        });
      }

      // Check if the requesting user is one of the two users
      const currentUserId = req.user._id.toString();
      if (currentUserId !== userId1 && currentUserId !== userId2) {
        return res.status(403).json({
          success: false,
          message: "You can only check chats that you are a part of",
        });
      }

      // Find a non-group chat that has exactly these two participants
      const chat = await Chat.findOne({
        participants: { $all: [userId1, userId2], $size: 2 },
        isGroupChat: false,
      }).populate("invitationId");

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "No chat found between these users",
          exists: false,
        });
      }

      // Get chat status for the current user
      const chatStatus = await ChatController._getTwoUserChatStatus(
        chat,
        currentUserId
      );

      return res.status(200).json({
        success: true,
        message: "Chat found between users",
        exists: true,
        chat: {
          _id: chat._id,
          invitationId: chat.invitationId?._id || chat.invitationId,
          participants: chat.participants,
          isGroupChat: chat.isGroupChat,
          active: chat.active,
          createdAt: chat.createdAt,
          updatedAt: chat.updatedAt,
        },
        status: chatStatus,
      });
    } catch (error) {
      console.error("Error checking existing chat:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  /**
   * Check message limit for a user in a chat
   * @param {Object} req - Request object
   * @param {Object} res - Response object
   * @returns {Promise<Object>} - Response
   */
  async checkMessageLimit(req, res) {
    try {
      const { chatId } = req.params;
      const userId = req.user._id;

      // Find the chat
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Check if user is a participant
      if (
        !chat.participants.includes(userId) &&
        chat.invitationId.creator.toString() !== userId.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to access this chat",
        });
      }

      // Get user details to check premium status
      const user = await User.findById(userId);
      const isPremium = user && user.isPremium;

      // Count messages sent by this user in Firebase
      let messageCount = 0;
      let canSendMoreMessages = true;
      let remainingMessages = 0;

      try {
        // For group chats
        if (chat.isGroupChat) {
          const messagesQuery = await db
            .collection("messages")
            .where("chatId", "==", chat.invitationId._id.toString())
            .where("fromId", "==", userId.toString())
            .get();

          messageCount = messagesQuery.size;
        }
        // For individual chats
        else if (chat.participants.length === 2) {
          const sortedParticipants = [...chat.participants]
            .map((p) => p.toString())
            .sort();
          const chatRoomId = sortedParticipants.join("_");

          const chatRoomDoc = await db
            .collection("individual_chatrooms")
            .doc(chatRoomId)
            .get();

          if (chatRoomDoc.exists) {
            const chatData = chatRoomDoc.data();
            // Count messages where fromId matches the current user
            if (chatData.messages && Array.isArray(chatData.messages)) {
              messageCount = chatData.messages.filter(
                (msg) => msg.fromId === userId.toString() && !msg.isInfo
              ).length;
            }
          }
        }
      } catch (firebaseError) {
        console.warn("Error counting messages from Firebase:", firebaseError);
        // Fall back to MongoDB count if Firebase fails
        messageCount = chat.messages.filter(
          (msg) => msg.sender.toString() === userId.toString() && !msg.isInfo
        ).length;
      }

      // Check if user can send more messages
      const MESSAGE_LIMIT_NON_PREMIUM = 2;

      if (!isPremium && messageCount >= MESSAGE_LIMIT_NON_PREMIUM) {
        canSendMoreMessages = false;
        remainingMessages = 0;
      } else if (!isPremium) {
        remainingMessages = MESSAGE_LIMIT_NON_PREMIUM - messageCount;
      } else {
        // Premium users have unlimited messages
        remainingMessages = -1; // -1 indicates unlimited
      }

      return res.json({
        success: true,
        isPremium,
        messageCount,
        canSendMoreMessages,
        remainingMessages,
        messageLimit: isPremium ? -1 : MESSAGE_LIMIT_NON_PREMIUM,
      });
    } catch (error) {
      console.error("Error checking message limit:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  // removeMessage
  async removeMessage(req, res) {
    try {
      const { chatId, messageId } = req.params;
      const userId = req.user._id;

      // Find the chat
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        return res.status(404).json({
          success: false,
          message: "Chat not found",
        });
      }

      // Check if user is a participant
      if (
        !chat.participants.includes(userId) &&
        chat.invitationId.creator.toString() !== userId.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "Not authorized to remove this message",
        });
      }

      // Remove message from MongoDB
      await Chat.updateOne(
        { _id: chatId },
        { $pull: { messages: { _id: messageId } } }
      );

      // Remove message from Firebase
      // try {
      //   // Remove from messages collection
      //   await db.collection("messages").doc(messageId).delete();
      // } catch (firebaseError) {
      //   console.warn("Error removing message from Firebase:", firebaseError);
      //   // Continue with the response even if Firebase removal fails
      // }

      return res.json({
        success: true,
        message: "Message removed successfully",
      });
    } catch (error) {
      console.error("Error removing message:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
}

module.exports = new ChatController();
