const socketIO = require("socket.io");
const jwt = require("jsonwebtoken");
const User = require("../models/User/User");
const Chat = require("../models/Common/Chat");
const Invitation = require("../models/User/invitations");
// const firebaseChatService = require("./firebaseChatService"); // Removed Firebase dependency for chat
const { default: mongoose } = require("mongoose");
require("dotenv").config();

/**
 * Format a date to a readable string
 * @param {Date} date - Date to format
 * @returns {String} - Formatted date string
 */
function formatDate(date) {
  if (!date) return "";
  const options = {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  };
  return new Date(date).toLocaleDateString("en-US", options);
}

/**
 * Socket.IO Service for real-time chat
 */
class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socket.id
    this.userSockets = new Map(); // socket.id -> userId
  }

  /**
   * Initialize Socket.IO server
   * @param {Object} server - HTTP server instance
   */
  initialize(server) {
    this.io = socketIO(server, {
      cors: {
        origin: "*", // In production, restrict this to your app domains
        methods: ["GET", "POST"],
        credentials: true,
      },
      transports: ["websocket", "polling"],
      pingTimeout: 60000,
      pingInterval: 25000,
    });

    console.log("Socket.IO server initialized");

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        // Get token from query params for easier testing
        const token =
          socket.handshake.auth.token || socket.handshake.query.token;
        if (!token) {
          return next(new Error("Authentication token is required"));
        }
        //console.log("token", token);
        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        if (!decoded) {
          return next(new Error("Invalid authentication token"));
        }

        // Get user from database
        const user = await User.findById(decoded._id);

        if (!user) {
          return next(new Error("User not found"));
        }

        // Attach user to socket
        socket.user = {
          _id: user._id.toString(),
          name: user.userName || user.name,
          isPremium: user.isPremium || false,
        };

        next();
      } catch (error) {
        console.error("Socket authentication error:", error);
        next(new Error("Authentication failed: " + error.message));
      }
    });

    // Handle connections
    this.io.on("connection", (socket) => {
      this._handleConnection(socket);
    });

    return this.io;
  }

  /**
   * Handle new socket connection
   * @param {Object} socket - Socket instance
   * @private
   */
  _handleConnection(socket) {
    const userId = socket.user._id;
    console.log(`User connected: ${userId}, socket ID: ${socket.id}`);

    // Store user connection
    this.connectedUsers.set(userId, socket.id);
    this.userSockets.set(socket.id, userId);

    // Join user to their own room for private messages
    socket.join(userId);

    // Log all registered event listeners
    console.log(
      `Registered event handlers for socket ${socket.id}:`,
      socket.eventNames ? socket.eventNames() : "Event names not available"
    );

    // Handle joining chat rooms
    socket.on("join_chat", (data) => {
      console.log(`Received join_chat event from ${socket.id}:`, data);
      this._handleJoinChat(socket, data);
    });

    // Handle sending messages
    socket.on("send_message", (data) => {
      console.log(`Received send_message event from ${socket.id}:`, data);
      this._handleSendMessage(socket, data);
    });

    // Handle typing indicators
    socket.on("typing", (data) => {
      console.log(`Received typing event from ${socket.id}:`, data);
      this._handleTyping(socket, data);
    });

    // Handle read receipts
    socket.on("mark_read", (data) => {
      console.log(`Received mark_read event from ${socket.id}:`, data);
      this._handleMarkRead(socket, data);
    });

    // Handle disconnection
    socket.on("disconnect", () => {
      console.log(`Socket ${socket.id} disconnected`);
      this._handleDisconnect(socket);
    });

    // Send a welcome message to confirm connection
    socket.emit("connection_success", {
      userId: socket.user._id,
      socketId: socket.id,
      message: "Successfully connected to chat server",
    });
  }

  /**
   * Handle joining a chat room
   * @param {Object} socket - Socket instance
   * @param {Object} data - Chat data { chatId }
   * @private
   */
  async _handleJoinChat(socket, data) {
    try {
      console.log("data", data);
      console.log("socket", socket.server);
      const { chatId, userTime } = data;
      const userId = socket.user._id;
      console.log("userTime", userTime);
      console.log(`Attempting to join chat ${chatId} for user ${userId}`);

      // Validate chat exists and user is a participant
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        console.error(`Chat not found: ${chatId}`);
        return this._sendError(socket, "Chat not found");
      }

      console.log(
        `Found chat: ${chat._id}, participants: ${chat.participants}`
      );
      console.log(`Invitation by: ${chat.invitationId.invitationBy}`);

      // Convert ObjectId to string for comparison
      const participantStrings = chat.participants.map((p) => p.toString());
      const isParticipant = participantStrings.includes(userId);
      const isCreator = chat.invitationId.invitationBy.toString() === userId;

      console.log(
        `User is participant: ${isParticipant}, is creator: ${isCreator}`
      );

      if (!isParticipant && !isCreator) {
        return this._sendError(socket, "Not authorized to join this chat");
      }

      // Join the chat room
      const roomId = `chat:${chatId}`;
      socket.join(roomId);
      console.log("socket.user.time", userTime);
      // Get chat status
      const status = await this._getChatStatus(chat, userId, userTime);

      // Send chat status to the user
      socket.emit("chat_status", {
        chatId,
        status: status.status,
        statusColor: status.statusColor,
        statusMessage: status.statusMessage,
        chatOpenTime: status.chatOpenTime,
        chatCloseTime: status.chatCloseTime,
        timeUntilOpen: status.timeUntilOpen,
        canSendMessage: status.canSendMessage,
      });

      console.log(`User ${userId} successfully joined chat ${chatId}`);
    } catch (error) {
      console.error("Error joining chat:", error);
      this._sendError(socket, "Failed to join chat: " + error.message);
    }
  }

  /**
   * Handle sending a message
   * @param {Object} socket - Socket instance
   * @param {Object} data - Message data { chatId, content, isAudio, audioUrl, isInfo }
   * @private
   */
  async _handleSendMessage(socket, data) {
    try {
      const { chatId, content, isAudio, audioUrl, isInfo } = data;
      const userId = socket.user._id;
      const isPremium = socket.user.isPremium;

      console.log(
        `Attempting to send message to chat ${chatId} from user ${userId}`
      );

      // Get chat by ID and check if user is a participant
      const chat = await Chat.findById(chatId).populate("invitationId");

      if (!chat) {
        console.error(`Chat not found: ${chatId}`);
        return this._sendError(socket, "Chat not found");
      }

      // Convert ObjectId to string for comparison
      const participantStrings = chat.participants.map((p) => p.toString());
      const isParticipant = participantStrings.includes(userId);
      const isCreator = chat.invitationId.invitationBy.toString() === userId;

      console.log(
        `User is participant: ${isParticipant}, is creator: ${isCreator}`
      );

      if (!isParticipant && !isCreator) {
        return this._sendError(
          socket,
          "Not authorized to send messages to this chat"
        );
      }
      const userTime = data.userTime;
      console.log("userTime", userTime);
      // Check if chat is active and user can send messages
      const chatStatus = await this._getChatStatus(chat, userId, userTime);

      console.log(`Chat status for ${chatId}: ${JSON.stringify(chatStatus)}`);

      // Allow info messages to be sent regardless of chat status
      if (!chatStatus.canSendMessage && !isInfo) {
        return this._sendError(socket, "Chat is not active yet", {
          status: chatStatus.status,
          statusMessage: chatStatus.statusMessage,
          timeUntilOpen: chatStatus.timeUntilOpen,
          chatOpenTime: chatStatus.chatOpenTime,
          chatCloseTime: chatStatus.chatCloseTime,
          canSendMessage: chatStatus.canSendMessage,
        });
      }

      // For non-premium users, check message limit (except for info messages)
      if (!isPremium && !isInfo) {
        const messageCount = this._countUserMessages(chat, userId);
        const MESSAGE_LIMIT_NON_PREMIUM = 2;

        console.log(
          `User ${userId} message count: ${messageCount}, limit: ${MESSAGE_LIMIT_NON_PREMIUM}`
        );

        if (messageCount >= MESSAGE_LIMIT_NON_PREMIUM) {
          return this._sendError(
            socket,
            "Message limit reached for non-premium users",
            {
              isPremium: false,
              messageCount,
              messageLimit: MESSAGE_LIMIT_NON_PREMIUM,
              upgradeRequired: true,
            }
          );
        }
      }

      // Validate message content
      if (isAudio) {
        if (!audioUrl) {
          return this._sendError(
            socket,
            "Audio URL is required for audio messages"
          );
        }
      } else if (!content || content.trim() === "") {
        return this._sendError(
          socket,
          "Message content is required for text messages"
        );
      }

      // Process message through Firebase
      const messageData = {
        chatId,
        senderId: userId,
        content,
        isAudio: isAudio || false,
        audioUrl: audioUrl || null,
        isInfo: isInfo || false,
      };

      console.log(`Processing message: ${JSON.stringify(messageData)}`);

      // Save message to Firebase
      // const savedMessage = await firebaseChatService.validateAndProcessMessage(
      //   messageData
      // );
      // Create new message object
      const newMessage = {
        sender: userId,
        content,
        isAudio,
        audioUrl: isAudio ? audioUrl : null,
        isInfo,
        timestamp: new Date(),
        read: [
          {
            user: userId,
            readAt: new Date(),
          },
        ],
      };

      // Add message to chat and save
      chat.messages.push(newMessage);
      chat.updatedAt = new Date();
      const updatedChat = await chat.save();

      // Get the newly added message (last in array)
      const savedMessage =
        updatedChat.messages[updatedChat.messages.length - 1];

      // Populate sender info if not already populated
      if (
        typeof savedMessage.sender === "string" ||
        savedMessage.sender instanceof mongoose.Types.ObjectId
      ) {
        // await Chat.populate(updatedChat);
        savedMessage.sender =
          updatedChat.messages[updatedChat.messages.length - 1].sender;
      }
      if (!savedMessage) {
        console.error("Failed to save message to Database");
        return this._sendError(socket, "Failed to save message");
      }

      console.log(`Message saved successfully: ${savedMessage._id}`);

      // Get sender info
      const sender = await User.findById(userId, "_id userName coverImage");

      // Process read status for each participant
      const readStatus = {};

      // For each participant in the chat
      for (const participantId of chat.participants) {
        const participantIdStr = participantId.toString();
        // Check if the message has been read by this participant
        const readInfo = this.isMessageReadByUser(
          savedMessage,
          participantIdStr
        );
        readStatus[participantIdStr] = {
          isRead: readInfo.isRead,
          readAt: readInfo.readAt,
        };
      }

      // Prepare message for broadcast
      const messageToSend = {
        _id: savedMessage._id,
        chatId,
        content: messageData.content,
        isAudio: messageData.isAudio,
        audioUrl: messageData.audioUrl,
        isInfo: messageData.isInfo,
        sender: sender._id,
        timestamp: savedMessage.timestamp || new Date(),
        read: savedMessage.read,
        readStatus: readStatus, // Add the processed read status information
      };

      console.log(
        `Broadcasting message to chat ${chatId}: ${JSON.stringify(
          messageToSend
        )}`
      );

      // Broadcast message to all users in the chat
      this.emitToChat(chatId, "new_message", messageToSend);

      // Also send back to the sender to confirm receipt
      socket.emit("message_sent", {
        success: true,
        messageId: savedMessage._id,
        timestamp: messageToSend.timestamp,
      });

      // Send push notifications to offline users
      await this._sendPushNotifications(chat, messageToSend, userId);

      console.log(`Message sent to chat ${chatId} by user ${userId}`);
    } catch (error) {
      console.error("Error sending message:", error);
      this._sendError(socket, "Failed to send message: " + error.message);
    }
  }

  /**
   * Handle typing indicator
   * @param {Object} socket - Socket instance
   * @param {Object} data - Typing data { chatId, isTyping }
   * @private
   */
  _handleTyping(socket, data) {
    try {
      const { chatId, isTyping } = data;
      const userId = socket.user._id;
      const userName = socket.user.name;

      // Broadcast typing status to all users in the chat room except sender
      socket.to(`chat:${chatId}`).emit("typing_indicator", {
        chatId,
        userId,
        userName,
        isTyping,
      });
    } catch (error) {
      console.error("Error handling typing indicator:", error);
    }
  }

  /**
   * Check if a message has been read by a specific user
   * @param {Object} message - Message object
   * @param {String} userId - User ID to check
   * @returns {Object} - { isRead: boolean, readAt: Date|null }
   */
  isMessageReadByUser(message, userId) {
    // Convert userId to string for comparison
    const userIdStr = userId.toString();

    // Skip check for messages sent by this user (they're automatically considered read)
    if (message.sender.toString() === userIdStr) {
      return { isRead: true, readAt: message.timestamp };
    }

    // Find if user is in the read array
    const readEntry = message.read.find((r) => r.user.toString() === userIdStr);

    if (readEntry && readEntry.readAt) {
      return { isRead: true, readAt: readEntry.readAt };
    }

    return { isRead: false, readAt: null };
  }

  /**
   * Mark messages as read (API version - no socket required)
   * @param {String} chatId - Chat ID
   * @param {String} userId - User ID
   * @returns {Promise<Object>} - Result with success status and count
   */
  async markMessagesAsRead(chatId, userId) {
    try {
      console.log(
        `API: Marking messages as read in chat ${chatId} for user ${userId}`
      );

      // Get the chat to find unread messages
      const chat = await Chat.findById(chatId);
      if (!chat) {
        console.error(`Chat not found: ${chatId}`);
        return { success: false, message: "Chat not found" };
      }

      let modifiedCount = 0;
      const now = new Date();
      let hasUpdates = false;
      const userIdStr = userId.toString();

      // Process each message in the chat
      for (let i = 0; i < chat.messages.length; i++) {
        const message = chat.messages[i];

        // Skip messages sent by this user (they're automatically marked as read when sent)
        if (message.sender.toString() === userIdStr) {
          continue;
        }

        // Check if user is already in the read array
        const readIndex = message.read.findIndex(
          (r) => r.user.toString() === userIdStr
        );

        if (readIndex >= 0) {
          // User is in read array - update readAt if it's null
          if (!message.read[readIndex].readAt) {
            message.read[readIndex].readAt = now;
            hasUpdates = true;
            modifiedCount++;
            console.log(
              `Updated existing read status for message ${message._id}`
            );
          }
        } else {
          // User is not in read array - add them
          message.read.push({
            user: userId,
            readAt: now,
          });
          hasUpdates = true;
          modifiedCount++;
          console.log(`Added new read status for message ${message._id}`);
        }
      }

      // Save the chat if there were any updates
      if (hasUpdates) {
        chat.markModified("messages"); // Ensure Mongoose detects changes to the nested array
        await chat.save();
        console.log(`Saved ${modifiedCount} read status updates to database`);
      } else {
        console.log("No messages needed to be marked as read");
      }

      // Prepare detailed read status information for each message that was updated
      const updatedMessages = [];

      if (modifiedCount > 0) {
        // For each message in the chat
        chat.messages.forEach((message) => {
          // Process read status for this message
          const readStatus = {};

          // For each participant in the chat
          for (const participantId of chat.participants) {
            const participantIdStr = participantId.toString();
            // Check if the message has been read by this participant
            const readInfo = this.isMessageReadByUser(
              message,
              participantIdStr
            );
            readStatus[participantIdStr] = {
              isRead: readInfo.isRead,
              readAt: readInfo.readAt,
            };
          }

          // Add to the list of updated messages with their read status
          updatedMessages.push({
            messageId: message._id,
            readStatus: readStatus,
          });
        });

        // Broadcast read status to all users in the chat room
        this.io.to(`chat:${chatId}`).emit("messages_read", {
          chatId,
          userId,
          count: modifiedCount,
          timestamp: now,
          updatedMessages: updatedMessages,
        });
      }

      return {
        success: true,
        count: modifiedCount,
        message: "Messages marked as read successfully",
        updatedMessages,
      };
    } catch (error) {
      console.error("Error marking messages as read (API):", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle marking messages as read from socket connection
   * @param {Object} socket - Socket instance
   * @param {Object} data - Read data { chatId }
   * @private
   */
  async _handleMarkRead(socket, data) {
    try {
      const { chatId } = data;
      const userId = socket.user._id;

      // Use the API version to mark messages as read
      const result = await this.markMessagesAsRead(chatId, userId);

      // Send confirmation back to the requesting client
      socket.emit("mark_read_confirmed", {
        chatId,
        count: result.count,
        success: result.success,
        updatedMessages: result.updatedMessages,
      });

      return result;
    } catch (error) {
      console.error("Error in socket mark read handler:", error);
      this._sendError(
        socket,
        "Failed to mark messages as read: " + error.message
      );
      return { success: false, error: error.message };
    }
  }

  /*/ ... (rest of the code remains the same)
   * Handle socket disconnection
   * @param {Object} socket - Socket instance
   * @private
   */
  _handleDisconnect(socket) {
    const userId = this.userSockets.get(socket.id);

    if (userId) {
      console.log(`User disconnected: ${userId}`);
      this.connectedUsers.delete(userId);
      this.userSockets.delete(socket.id);
    }
  }

  /**
   * Send error message to client
   * @param {Object} socket - Socket instance
   * @param {String} message - Error message
   * @param {Object} data - Additional data
   * @private
   */
  _sendError(socket, message, data = {}) {
    socket.emit("error", {
      message,
      ...data,
    });
  }

  /**
   * Generate status update message for invitation response
   * @param {String} action - Action taken (accepted, declined, rescheduled, left)
   * @param {String} userId - User ID who performed the action
   * @param {String} targetUserId - User ID who received the action
   * @param {Boolean} isGroup - Whether this is a group chat
   * @param {String} userName - Name of the user who performed the action
   * @returns {Object} - Messages for both users
   * @private
   */
  async _generateStatusMessage(
    action,
    userId,
    targetUserId,
    isGroup,
    userName
  ) {
    // Default messages
    let userMessage = "";
    let targetMessage = "";

    if (isGroup) {
      // Group chat messages
      switch (action) {
        case "accepted":
          userMessage = "You joined the group.";
          targetMessage = `${userName} joined the group.`;
          break;
        case "declined":
          userMessage = "You declined to join the group.";
          targetMessage = `${userName} declined to join the group.`;
          break;
        case "left":
          userMessage = "You left the group.";
          targetMessage = `${userName} left the group.`;
          break;
        case "rescheduled":
          userMessage = "You rescheduled the group meetup.";
          targetMessage = `${userName} rescheduled the group meetup.`;
          break;
        default:
          userMessage = `You ${action} the invitation.`;
          targetMessage = `${userName} ${action} the invitation.`;
      }
    } else {
      // One-to-one chat messages
      switch (action) {
        case "accepted":
          userMessage = "You accepted the invitation.";
          targetMessage = `${userName} accepted your invitation.`;
          break;
        case "declined":
          userMessage = "You declined the invitation.";
          targetMessage = `${userName} declined your invitation.`;
          break;
        case "rescheduled":
          userMessage = "You rescheduled the meetup.";
          targetMessage = `${userName} rescheduled the meetup.`;
          break;
        default:
          userMessage = `You ${action} the invitation.`;
          targetMessage = `${userName} ${action} the invitation.`;
      }
    }

    return {
      userMessage,
      targetMessage,
    };
  }

  /**
   * Send status update messages for invitation responses
   * @param {String} chatId - Chat ID
   * @param {String} action - Action taken (accepted, declined, rescheduled)
   * @param {String} userId - User ID who performed the action
   * @param {String} targetUserId - User ID who received the action (usually creator)
   * @param {Boolean} isGroup - Whether this is a group chat
   * @returns {Promise<void>}
   */
  async sendInvitationResponseMessages(
    chatId,
    action,
    userId,
    targetUserId,
    isGroup = false
  ) {
    try {
      console.log("targetUserId _id", targetUserId._id);
      // Ensure we have string IDs
      const userIdString =
        typeof userId === "object" && userId !== null && userId._id
          ? userId._id.toString()
          : userId.toString();

      const targetUserIdString =
        typeof targetUserId === "object" &&
        targetUserId !== null &&
        targetUserId._id
          ? targetUserId._id.toString()
          : targetUserId.toString();
      console.log("targetUserIdString", targetUserIdString);
      // Get user details
      const user = await User.findById(userIdString).select("userName");
      if (!user) {
        console.error("User not found for status message");
        return;
      }

      const userName = user.userName || "User";

      // Generate appropriate messages
      const { userMessage, targetMessage } = await this._generateStatusMessage(
        action,
        userIdString,
        targetUserIdString,
        isGroup,
        userName
      );

      // Log what we're sending to help debug
      console.log(
        `Sending info message to ${userIdString} in chat ${chatId}: ${userMessage}`
      );
      console.log(
        `Sending info message to ${targetUserIdString} in chat ${chatId}: ${targetMessage}`
      );

      // Send info message to the user who performed the action
      // await this.sendInfoMessage(chatId, userIdString, userMessage);

      // // Send info message to the target user (usually creator)
      // await this.sendInfoMessage(chatId, targetUserIdString, targetMessage);

      // Emit to chat to update in real-time
      this.emitToChat(chatId, "status_update", {
        action,
        userId: userIdString,
        userName,
        timestamp: new Date(),
      });
    } catch (error) {
      console.error("Error sending invitation response messages:", error);
    }
  }

  /**
   * Get chat status based on invitation and user type
   * @param {Object} chat - Chat object
   * @param {String} userId - User ID
   * @returns {Object} - Chat status
   * @private
   */
  // async _getChatStatus(chat, userId, userTime) {
  //   try {
  //     // Default status
  //     let status = {
  //       status: "Pending",
  //       statusColor: "FFA500", // Orange
  //       statusMessage: "Chat is not active yet.",
  //       canSendMessage: false,
  //       timeUntilOpen: null,
  //     };
  //     console.log("userTime in status", userTime);
  //     const invitation = await Invitation.findById(chat.invitationId).populate(
  //       "users"
  //     );

  //     const meetingTime = new Date(invitation.date);
  //     const now = new Date(userTime);

  //     // Get user to check premium status
  //     const user = await User.findById(userId);
  //     const isPremium = user && user.isPremium;

  //     // Calculate chat open and close times using proper time arithmetic
  //     const chatCloseTime = new Date(
  //       meetingTime.getTime() + 1 * 60 * 60 * 1000
  //     ); // 1 hour after meeting
  //     console.log("chatCloseTime", chatCloseTime);
  //     let chatOpenTime;
  //     if (isPremium) {
  //       chatOpenTime = new Date(meetingTime.getTime() - 24 * 60 * 60 * 1000); // 24 hours before for premium
  //     } else {
  //       chatOpenTime = new Date(meetingTime.getTime() - 3 * 60 * 60 * 1000); // 3 hours before for standard
  //     }
  //     console.log("chatOpenTime 852", chatOpenTime);
  //     // Determine chat status
  //     if (now >= chatCloseTime) {
  //       // Chat is closed (after meeting + 1 hour)
  //       status = {
  //         status: "Closed",
  //         statusColor: "FF0000", // Red
  //         statusMessage: "This chat is now closed.",
  //         canSendMessage: false,
  //         timeUntilOpen: null,
  //       };
  //     } else if (now >= chatOpenTime) {
  //       // Chat is active
  //       status = {
  //         status: "Open now",
  //         statusColor: "39FF14", // Green
  //         statusMessage: "Chat is open for coordination.",
  //         canSendMessage: true,
  //         timeUntilOpen: chatCloseTime,
  //       };
  //     } else {
  //       // Chat will open soon
  //       const timeUntilOpen =
  //         chatOpenTime.getTime() - new Date(userTime).getTime();
  //       const minutesUntilOpen = Math.ceil(timeUntilOpen / (1000 * 60));
  //       const hoursUntilOpen = Math.ceil(timeUntilOpen / (1000 * 60 * 60));

  //       // Format the status message based on time until open
  //       let statusText = "";
  //       let statusMessage = "";
  //       console.log("hoursUntilOpen", hoursUntilOpen);
  //       console.log("timeUntilOpen", timeUntilOpen);
  //       if (timeUntilOpen <= 0) {
  //         // Should not happen, but handle edge case
  //         statusText = "Opens now";
  //         statusMessage = "Chat is opening now";
  //       } else if (minutesUntilOpen < 60) {
  //         // Less than 1 hour - show minutes
  //         statusText = `Opens in ${minutesUntilOpen} minute${
  //           minutesUntilOpen > 1 ? "s" : ""
  //         }`;
  //         statusMessage = `Chat opens in ${minutesUntilOpen} minute${
  //           minutesUntilOpen > 1 ? "s" : ""
  //         }`;
  //       } else if (hoursUntilOpen < 24) {
  //         // Less than 24 hours - show hours
  //         statusText = `Opens in ${hoursUntilOpen} hour${
  //           hoursUntilOpen > 1 ? "s" : ""
  //         }`;
  //         statusMessage = `Chat opens in ${hoursUntilOpen} hour${
  //           hoursUntilOpen > 1 ? "s" : ""
  //         }`;
  //       } else {
  //         // 24 hours or more - show days
  //         const daysUntilOpen = Math.floor(hoursUntilOpen / 24);
  //         const remainingHours = hoursUntilOpen % 24;

  //         if (remainingHours === 0) {
  //           statusText = `Opens in ${daysUntilOpen} day${
  //             daysUntilOpen > 1 ? "s" : ""
  //           }`;
  //           statusMessage = `Chat opens in ${daysUntilOpen} day${
  //             daysUntilOpen > 1 ? "s" : ""
  //           }`;
  //         } else {
  //           statusText = `Opens in ${daysUntilOpen} day${
  //             daysUntilOpen > 1 ? "s" : ""
  //           } ${remainingHours}h`;
  //           statusMessage = `Chat opens in ${daysUntilOpen} day${
  //             daysUntilOpen > 1 ? "s" : ""
  //           } and ${remainingHours} hour${remainingHours > 1 ? "s" : ""}`;
  //         }
  //       }

  //       // Add specific time information
  //       const openTimeFormatted = chatOpenTime.toLocaleString("en-US", {
  //         weekday: "short",
  //         month: "short",
  //         day: "numeric",
  //         hour: "2-digit",
  //         minute: "2-digit",
  //         hour12: false,
  //       });

  //       // statusMessage += ` (${openTimeFormatted})`;

  //       status = {
  //         status: statusText,
  //         statusColor: "FFA500", // Orange
  //         statusMessage: statusMessage,
  //         chatOpenTime: chatOpenTime,
  //         canSendMessage: false,
  //         timeUntilOpen: chatOpenTime,
  //       };
  //     }
  //     console.log("status", status);
  //     return status;
  //   } catch (error) {
  //     console.error("Error getting chat status:", error);
  //     return {
  //       status: "Error",
  //       statusColor: "FF0000",
  //       statusMessage: "Error determining chat status",
  //       canSendMessage: false,
  //     };
  //   }
  // }
  async _getChatStatus(chat, userId, userTime) {
    try {
      // Default status
      let status = {
        status: "Pending",
        statusColor: "FFA500", // Orange
        statusMessage: "Chat is not active yet.",
        canSendMessage: false,
        timeUntilOpen: null,
      };

      const invitation = await Invitation.findById(chat.invitationId).populate(
        "users"
      );
      if (!invitation) {
        return status;
      }

      // Use userTime if provided, otherwise use server time
      const now = userTime ? new Date(userTime) : new Date();
      const meetingTime = new Date(invitation.date);

      // Get user to check premium status
      const user = await User.findById(userId);
      const isPremium = user && user.isPremium;

      // Calculate chat open and close times
      const chatCloseTime = new Date(meetingTime.getTime() + 60 * 60 * 1000); // 1 hour after meeting
      const chatOpenTime = new Date(
        meetingTime.getTime() - (isPremium ? 24 : 3) * 60 * 60 * 1000
      ); // 24h before for premium, 3h for standard

      // Format time in 24h European format
      const formatTime = (date) => {
        return date.toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        });
      };

      // Format date in European format
      const formatDate = (date) => {
        return date.toLocaleDateString("en-GB", {
          weekday: "long",
          day: "numeric",
          month: "long",
        });
      };

      // Determine chat status
      if (now >= chatCloseTime) {
        status = {
          status: "Closed",
          statusColor: "FF0000",
          statusMessage: "This chat is now closed.",
          canSendMessage: false,
          timeUntilOpen: null,
        };
      } else if (now >= chatOpenTime) {
        status = {
          status: "Open now",
          statusColor: "39FF14",
          statusMessage: "Chat is open for coordination.",
          canSendMessage: true,
          timeUntilOpen: chatCloseTime,
        };
      } else {
        // Chat will open in the future
        const timeUntilOpenMs = chatOpenTime - now;
        const hoursUntilOpen = Math.ceil(timeUntilOpenMs / (1000 * 60 * 60));
        const daysUntilOpen = Math.floor(hoursUntilOpen / 24);
        const remainingHours = hoursUntilOpen % 24;

        let statusText, statusMessage;

        if (hoursUntilOpen < 24) {
          // Less than 24 hours
          statusText = `Opens in ${hoursUntilOpen} hour${
            hoursUntilOpen !== 1 ? "s" : ""
          }`;
          statusMessage = `Opens at ${formatTime(
            chatOpenTime
          )} (in ${hoursUntilOpen} hour${hoursUntilOpen !== 1 ? "s" : ""})`;
        } else {
          // 24 hours or more
          if (remainingHours === 0) {
            statusText = `Opens in ${daysUntilOpen} day${
              daysUntilOpen !== 1 ? "s" : ""
            }`;
            statusMessage = `Opens on ${formatDate(
              chatOpenTime
            )} at ${formatTime(chatOpenTime)} (in ${daysUntilOpen} day${
              daysUntilOpen !== 1 ? "s" : ""
            })`;
          } else {
            statusText = `Opens in ${daysUntilOpen} day${
              daysUntilOpen !== 1 ? "s" : ""
            } ${remainingHours}h`;
            statusMessage = `Opens on ${formatDate(
              chatOpenTime
            )} at ${formatTime(chatOpenTime)} (in ${daysUntilOpen} day${
              daysUntilOpen !== 1 ? "s" : ""
            } and ${remainingHours} hour${remainingHours !== 1 ? "s" : ""})`;
          }
        }

        status = {
          status: statusText,
          statusColor: "FFA500",
          statusMessage: statusMessage,
          chatOpenTime: chatOpenTime,
          canSendMessage: false,
          timeUntilOpen: chatOpenTime,
        };
      }

      return status;
    } catch (error) {
      console.error("Error getting chat status:", error);
      return {
        status: "Error",
        statusColor: "FF0000",
        statusMessage: "Error determining chat status",
        canSendMessage: false,
      };
    }
  }
  /**
   * Count messages sent by a user in a chat
   * @param {Object} chat - Chat object
   * @param {String} userId - User ID
   * @returns {Number} - Message count
   * @private
   */
  async _countUserMessages(chat, userId) {
    try {
      let messageCount = 0;

      // Try to count messages from Firebase
      try {
        // Get message count from MongoDB
        const chat = await Chat.findById(chat._id);
        messageCount = chat
          ? chat.messages.filter(
              (msg) => msg.senderId.toString() === userId.toString()
            ).length
          : 0;
        // Old Firebase code:
        // messageCount = await firebaseChatService.countUserMessages(chatId, userId);
        console.log(
          `MongoDB message count for user ${userId} in chat ${chat._id}: ${messageCount}`
        );
      } catch (firebaseError) {
        console.warn("Error counting messages from Firebase:", firebaseError);

        // Fall back to MongoDB count if Firebase fails
        if (chat.messages && Array.isArray(chat.messages)) {
          messageCount = chat.messages.filter(
            (msg) => msg.sender.toString() === userId.toString() && !msg.isInfo
          ).length;
          console.log(`MongoDB fallback message count: ${messageCount}`);
        }
      }

      return messageCount;
    } catch (error) {
      console.error("Error counting messages:", error);
      // Return 0 if all counting methods fail
      return 0;
    }
  }

  /**
   * Send an info message directly to MongoDB
   * @param {String} chatId - Chat ID or Invitation ID
   * @param {String|Object} senderId - Sender user ID or user object
   * @param {String} message - Info message content
   * @returns {Promise<Object>} - Result with the saved message
   */
  async sendInfoMessage(chatId, senderId, message) {
    try {
      console.log("senderId", senderId);
      // Extract user ID if a user object was passed
      let senderIdString;
      if (typeof senderId === "object" && senderId !== null) {
        console.log("Sender is an object:", senderId);
        // If it's a Mongoose object with _id
        if (senderId._id) {
          senderIdString = senderId._id.toString();
        } else {
          // Try to stringify the object to log what we received
          console.log(
            "Invalid sender object received:",
            JSON.stringify(senderId)
          );
          return { error: "Invalid sender ID format" };
        }
      } else {
        // It's already a string or primitive that can be converted to string
        senderIdString = senderId.toString();
      }

      // Find the chat by invitation ID first
      let chat = await Chat.findOne({ invitationId: chatId });

      // If not found, try finding by chat ID
      if (!chat) {
        chat = await Chat.findById(chatId);
      }

      if (!chat) {
        console.error(`Chat not found for ID: ${chatId}`);
        return { error: "Chat not found" };
      }

      const now = new Date();
      console.log("senderIdString", senderIdString);
      // Create the info message with proper ObjectId references
      const newMessage = {
        sender: senderIdString,
        content: message,
        isInfo: true,
        timestamp: now,
        read: [
          {
            user: senderIdString,
            readAt: now,
          },
        ],
      };

      // Add message to chat and save
      chat.messages.push(newMessage);
      chat.updatedAt = now;
      const updatedChat = await chat.save();

      // Get the newly added message (last in array)
      const savedMessage =
        updatedChat.messages[updatedChat.messages.length - 1];

      console.log(`Info message saved successfully: ${savedMessage._id}`);

      // Emit to chat to update in real-time
      this.emitToChat(chat._id.toString(), "new_message", {
        _id: savedMessage._id,
        chatId: chat._id.toString(),
        content: message,
        isInfo: true,
        sender: senderIdString,
        timestamp: now,
      });

      return { success: true, message: savedMessage };
    } catch (error) {
      console.error("Error sending info message:", error);
      return { error: "Failed to send info message: " + error.message };
    }
  }

  /**
   * Send push notifications to offline users
   * @param {Object} chat - Chat object
   * @param {Object} message - Message object
   * @param {String} senderId - Sender ID
   * @private
   */
  async _sendPushNotifications(chat, message, senderId) {
    try {
      // Get recipients (all participants except sender)
      const recipients = chat.participants.filter(
        (p) => p.toString() !== senderId
      );

      for (const recipientId of recipients) {
        // Check if recipient is online
        if (!this.connectedUsers.has(recipientId.toString())) {
          // User is offline, send push notification
          // This is a placeholder - you'll need to implement your push notification service
          console.log(`Should send push notification to user ${recipientId}`);

          // Example of how you might call your push notification service:
          // await pushNotificationService.sendChatNotification({
          //   userId: recipientId,
          //   senderName: message.sender.name,
          //   chatId: message.chatId,
          //   messageContent: message.isAudio ? 'Sent an audio message' : message.content,
          //   isGroupChat: chat.isGroupChat
          // });
        }
      }
    } catch (error) {
      console.error("Error sending push notifications:", error);
    }
  }

  /**
   * Emit event to specific user
   * @param {String} userId - User ID
   * @param {String} event - Event name
   * @param {Object} data - Event data
   */
  emitToUser(userId, event, data) {
    if (this.connectedUsers.has(userId)) {
      const socketId = this.connectedUsers.get(userId);
      this.io.to(socketId).emit(event, data);
    }
  }

  /**
   * Emit event to all users in a chat
   * @param {String} chatId - Chat ID
   * @param {String} event - Event name
   * @param {Object} data - Event data
   */
  emitToChat(chatId, event, data) {
    console.log(`Emitting event ${event} to chat ${chatId}`);
    this.io.to(`chat:${chatId}`).emit(event, data);
  }

  /**
   * Emit event to all connected users
   * @param {String} event - Event name
   * @param {Object} data - Event data
   */
  emitToAll(event, data) {
    this.io.emit(event, data);
  }
}

module.exports = new SocketService();
